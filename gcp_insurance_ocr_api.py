from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import cv2
import numpy as np
from PIL import Image
import io
import base64
from google.cloud import vision
import re
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor
import os
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Insurance Card OCR API",
    description="Real-time insurance card processing with OCR and quality assessment",
    version="1.0.0"
)

# Add CORS middleware for iOS app
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize Google Vision client
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'ca-healthcareinsightsplatform-8509ba7b2e54.json'
vision_client = vision.ImageAnnotatorClient()

# Thread pool for CPU-intensive tasks
executor = ThreadPoolExecutor(max_workers=4)

@dataclass
class ImageQuality:
    is_good_quality: bool
    blur_score: float
    brightness_score: float
    contrast_score: float
    resolution_score: float
    overall_score: float
    issues: List[str]

@dataclass
class InsuranceCardData:
    # Core identification (1-3)
    member_id: str = ""                    # 1. Member ID Number
    member_name: str = ""                  # 2. Member Name
    group_number: str = ""                 # 3. Group Number
    
    # Provider and plan info (4-5)
    pcp_name: str = ""                     # 4. Primary Care Provider Name
    pcp_phone: str = ""                    # 4. Primary Care Provider Phone
    plan_type: str = ""                    # 5. Plan Type
    
    # Copay information (6-8)
    copay_pcp: str = ""                    # 6. Co-Pay for PCP visits
    copay_specialist: str = ""             # 7. Co-Pay for Specialty Care
    copay_emergency: str = ""              # 8. Emergency Care Co-Pay
    copay_urgent_care: str = ""            # 8. Urgent Care Co-Pay
    
    # Prescription and additional info (9-10)
    rx_info: Dict[str, str] = None         # 9. Prescription Drug Plan Info
    plan_website: str = ""                 # 10. Health Plan Website
    
    # Deductibles and coinsurance (11-12)
    in_network_deductible: str = ""        # 11. In-Network Deductible
    in_network_coinsurance: str = ""       # 11. In-Network Coinsurance
    oon_deductible: str = ""               # 12. Out-of-Network Deductible
    oon_coinsurance: str = ""              # 12. Out-of-Network Coinsurance
    
    # Contact information (13)
    plan_phone: str = ""                   # 13. Plan Contact Phone
    customer_service_phone: str = ""       # 13. Customer Service Phone
    
    # Additional extracted fields
    insurance_company: str = ""
    effective_date: str = ""
    confidence_scores: Dict[str, float] = None

class ImageQualityAssessor:
    """Assess image quality for OCR processing"""
    
    @staticmethod
    def calculate_blur_score(image: np.ndarray) -> float:
        """Calculate blur score using Laplacian variance"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        # Normalize to 0-100 scale
        return min(laplacian_var / 10, 100)
    
    @staticmethod
    def calculate_brightness_score(image: np.ndarray) -> float:
        """Calculate brightness score"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        brightness = np.mean(gray)
        # Optimal brightness is around 127, penalize too dark or too bright
        optimal_brightness = 127
        brightness_score = 100 - abs(brightness - optimal_brightness) / optimal_brightness * 100
        return max(brightness_score, 0)
    
    @staticmethod
    def calculate_contrast_score(image: np.ndarray) -> float:
        """Calculate contrast score using standard deviation"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY) if len(image.shape) == 3 else image
        contrast = np.std(gray)
        # Normalize to 0-100 scale
        return min(contrast / 2, 100)
    
    @staticmethod
    def calculate_resolution_score(image: np.ndarray) -> float:
        """Calculate resolution adequacy score"""
        height, width = image.shape[:2]
        total_pixels = height * width
        # Consider 300x200 as minimum acceptable resolution
        min_pixels = 300 * 200
        if total_pixels >= min_pixels * 4:  # High resolution
            return 100
        elif total_pixels >= min_pixels:  # Acceptable resolution
            return 70 + (total_pixels - min_pixels) / (min_pixels * 3) * 30
        else:  # Low resolution
            return total_pixels / min_pixels * 70
    
    @classmethod
    def assess_quality(cls, image: np.ndarray) -> ImageQuality:
        """Comprehensive image quality assessment"""
        blur_score = cls.calculate_blur_score(image)
        brightness_score = cls.calculate_brightness_score(image)
        contrast_score = cls.calculate_contrast_score(image)
        resolution_score = cls.calculate_resolution_score(image)
        
        # Calculate overall score with weights
        overall_score = (
            blur_score * 0.3 +
            brightness_score * 0.25 +
            contrast_score * 0.25 +
            resolution_score * 0.2
        )
        
        # Identify issues
        issues = []
        if blur_score < 30:
            issues.append("Image is too blurry")
        if brightness_score < 40:
            issues.append("Image is too dark or too bright")
        if contrast_score < 30:
            issues.append("Low contrast - text may be hard to read")
        if resolution_score < 50:
            issues.append("Resolution is too low")
        
        is_good_quality = overall_score >= 60 and len(issues) == 0
        
        return ImageQuality(
            is_good_quality=is_good_quality,
            blur_score=blur_score,
            brightness_score=brightness_score,
            contrast_score=contrast_score,
            resolution_score=resolution_score,
            overall_score=overall_score,
            issues=issues
        )

class InsuranceCardProcessor:
    """Process insurance card images and extract data"""
    
    @staticmethod
    def preprocess_image(image: np.ndarray) -> np.ndarray:
        """Preprocess image for better OCR results"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Morphological operations to clean up
        kernel = np.ones((2, 2), np.uint8)
        processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return processed
    
    @staticmethod
    def extract_text_with_vision_api(image_bytes: bytes) -> Tuple[str, List[Dict]]:
        """Extract text using Google Vision API"""
        try:
            image = vision.Image(content=image_bytes)
            response = vision_client.text_detection(image=image)
            
            if response.error.message:
                raise Exception(f"Vision API error: {response.error.message}")
            
            # Get full text
            full_text = response.full_text_annotation.text if response.full_text_annotation else ""
            
            # Get individual text annotations with bounding boxes
            text_annotations = []
            for annotation in response.text_annotations[1:]:  # Skip the first one (full text)
                vertices = [(vertex.x, vertex.y) for vertex in annotation.bounding_poly.vertices]
                text_annotations.append({
                    "text": annotation.description,
                    "confidence": annotation.confidence if hasattr(annotation, 'confidence') else 0.9,
                    "bounding_box": vertices
                })
            
            return full_text, text_annotations
            
        except Exception as e:
            logger.error(f"Vision API error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"OCR processing failed: {str(e)}")
    
    @staticmethod
    def parse_insurance_data(text: str, annotations: List[Dict]) -> InsuranceCardData:
        """Parse extracted text to identify all 13 key insurance card fields"""
        data = InsuranceCardData()
        data.rx_info = {}
        data.confidence_scores = {}
        
        lines = text.split('\n')
        text_lower = text.lower()
        
        # Comprehensive patterns for all 13 fields
        patterns = {
            # 1. Member ID Number
            'member_id': [
                r'member\s*(?:id|#)[\s:]*([A-Z0-9]{6,})',
                r'subscriber\s*(?:id|#)[\s:]*([A-Z0-9]{6,})',
                r'id\s*(?:number)?[\s:]*([A-Z0-9]{6,})',
                r'member[\s:]*([A-Z0-9]{6,})'
            ],
            
            # 3. Group Number
            'group_number': [
                r'group\s*(?:number|#)[\s:]*([A-Z0-9]{4,})',
                r'grp[\s:]*([A-Z0-9]{4,})',
                r'group[\s:]*([A-Z0-9]{4,})',
                r'employer\s*group[\s:]*([A-Z0-9]{4,})'
            ],
            
            # 4. PCP Phone Number
            'pcp_phone': [
                r'pcp[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
                r'primary\s+care[\s\w]*phone[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
                r'physician[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})'
            ],
            
            # 5. Plan Type
            'plan_type': [
                r'plan\s*type[\s:]*([A-Z]{2,4})',
                r'(?:^|\s)(PPO|HMO|EPO|POS|HDHP)(?:\s|$)',
                r'coverage[\s:]*([A-Z]{2,4})'
            ],
            
            # 10. Website
            'plan_website': [
                r'(www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})',
                r'([a-zA-Z0-9.-]+\.com)',
                r'website[\s:]*([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
            ],
            
            # 13. Plan Contact Phone
            'plan_phone': [
                r'customer\s+service[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
                r'member\s+services[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
                r'plan[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})',
                r'contact[\s:]*(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})'
            ],
            
            # Effective Date
            'effective_date': [
                r'effective[\s:]*(\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4})',
                r'eff[\s:]*(\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4})',
                r'valid\s+from[\s:]*(\d{1,2}[/\-]\d{1,2}[/\-]\d{2,4})'
            ]
        }
        
        # Extract using patterns
        for field, field_patterns in patterns.items():
            for pattern in field_patterns:
                match = re.search(pattern, text_lower)
                if match:
                    setattr(data, field, match.group(1).strip())
                    data.confidence_scores[field] = 0.8
                    break
        
        # 2. Extract member name (usually at the top, before member ID)
        name_candidates = []
        for i, line in enumerate(lines[:8]):  # Check first 8 lines
            line = line.strip()
            # Skip company names and headers
            if (len(line) > 3 and not re.search(r'\d', line) and 
                len(line.split()) >= 2 and len(line.split()) <= 4 and
                not any(company in line.lower() for company in ['blue cross', 'aetna', 'cigna', 'anthem', 'humana', 'kaiser', 'united'])):
                name_candidates.append((i, line))
        
        if name_candidates:
            # Choose the most likely name (usually appears before member ID)
            data.member_name = name_candidates[0][1]
            data.confidence_scores['member_name'] = 0.75
        
        # Extract insurance company
        insurance_companies = [
            'aetna', 'anthem', 'blue cross blue shield', 'blue cross', 'blue shield',
            'cigna', 'humana', 'kaiser permanente', 'kaiser', 'united healthcare', 
            'unitedhealth', 'wellcare', 'molina', 'centene', 'healthnet'
        ]
        
        for company in insurance_companies:
            if company in text_lower:
                data.insurance_company = company.title()
                data.confidence_scores['insurance_company'] = 0.9
                break
        
        # 4. Extract PCP Name (usually appears with PCP phone or before it)
        pcp_patterns = [
            r'pcp[\s:]*([A-Za-z\s\.]{10,40}?)(?:\s*\(?\d{3}|\s*$)',
            r'primary\s+care\s+provider[\s:]*([A-Za-z\s\.]{10,40}?)(?:\s*\(?\d{3}|\s*$)',
            r'physician[\s:]*([A-Za-z\s\.]{10,40}?)(?:\s*\(?\d{3}|\s*$)'
        ]
        
        for pattern in pcp_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                pcp_name = match.group(1).strip()
                if len(pcp_name.split()) >= 2:  # Ensure it's a full name
                    data.pcp_name = pcp_name
                    data.confidence_scores['pcp_name'] = 0.7
                    break
        
        # 6-8. Extract copay information with specific categories
        copay_patterns = {
            'copay_pcp': [
                r'primary\s+care[\s:]*\$?(\d+)',
                r'pcp[\s:]*\$?(\d+)',
                r'office\s+visit[\s:]*\$?(\d+)'
            ],
            'copay_specialist': [
                r'specialist[\s:]*\$?(\d+)',
                r'specialty\s+care[\s:]*\$?(\d+)',
                r'spec[\s:]*\$?(\d+)'
            ],
            'copay_emergency': [
                r'emergency[\s:]*\$?(\d+)',
                r'er[\s:]*\$?(\d+)',
                r'emergency\s+room[\s:]*\$?(\d+)'
            ],
            'copay_urgent_care': [
                r'urgent\s+care[\s:]*\$?(\d+)',
                r'urgent[\s:]*\$?(\d+)'
            ]
        }
        
        for copay_type, patterns_list in copay_patterns.items():
            for pattern in patterns_list:
                match = re.search(pattern, text_lower)
                if match:
                    setattr(data, copay_type, f"${match.group(1)}")
                    data.confidence_scores[copay_type] = 0.8
                    break
        
        # 9. Extract prescription drug information
        rx_patterns = [
            r'generic[\s:]*\$?(\d+)',
            r'brand[\s:]*\$?(\d+)',
            r'preferred[\s:]*\$?(\d+)',
            r'non-preferred[\s:]*\$?(\d+)',
            r'specialty[\s:]*\$?(\d+)'
        ]
        
        for pattern in rx_patterns:
            matches = re.finditer(pattern, text_lower)
            for match in matches:
                context = text_lower[max(0, match.start()-15):match.start()]
                if 'generic' in pattern:
                    data.rx_info['generic'] = f"${match.group(1)}"
                elif 'brand' in pattern:
                    data.rx_info['brand'] = f"${match.group(1)}"
                elif 'preferred' in pattern:
                    data.rx_info['preferred'] = f"${match.group(1)}"
                elif 'specialty' in pattern:
                    data.rx_info['specialty'] = f"${match.group(1)}"
        
        # 11-12. Extract deductible and coinsurance information
        deductible_patterns = {
            'in_network_deductible': [
                r'in[-\s]network\s+deductible[\s:]*\$?(\d+)',
                r'deductible[\s:]*\$?(\d+)(?=.*in[-\s]network)',
                r'individual\s+deductible[\s:]*\$?(\d+)'
            ],
            'oon_deductible': [
                r'out[-\s]of[-\s]network\s+deductible[\s:]*\$?(\d+)',
                r'oon\s+deductible[\s:]*\$?(\d+)',
                r'deductible[\s:]*\$?(\d+)(?=.*out[-\s]of[-\s]network)'
            ]
        }
        
        for deductible_type, patterns_list in deductible_patterns.items():
            for pattern in patterns_list:
                match = re.search(pattern, text_lower)
                if match:
                    setattr(data, deductible_type, f"${match.group(1)}")
                    data.confidence_scores[deductible_type] = 0.75
                    break
        
        # Extract coinsurance
        coinsurance_patterns = {
            'in_network_coinsurance': [
                r'in[-\s]network.*?(\d+)%',
                r'coinsurance[\s:]*(\d+)%(?=.*in[-\s]network)'
            ],
            'oon_coinsurance': [
                r'out[-\s]of[-\s]network.*?(\d+)%',
                r'oon.*?(\d+)%',
                r'coinsurance[\s:]*(\d+)%(?=.*out[-\s]of[-\s]network)'
            ]
        }
        
        for coinsurance_type, patterns_list in coinsurance_patterns.items():
            for pattern in patterns_list:
                match = re.search(pattern, text_lower)
                if match:
                    setattr(data, coinsurance_type, f"{match.group(1)}%")
                    data.confidence_scores[coinsurance_type] = 0.75
                    break
        
        # Extract any additional phone numbers for customer service
        phone_matches = re.findall(r'(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})', text)
        if phone_matches:
            # Filter out already assigned phones
            assigned_phones = {data.pcp_phone, data.plan_phone}
            remaining_phones = [p for p in phone_matches if p not in assigned_phones]
            if remaining_phones and not data.customer_service_phone:
                data.customer_service_phone = remaining_phones[0]
                data.confidence_scores['customer_service_phone'] = 0.6
        
        return data

async def process_image_async(image_bytes: bytes) -> Tuple[ImageQuality, InsuranceCardData]:
    """Process image asynchronously"""
    loop = asyncio.get_event_loop()
    
    # Convert bytes to numpy array
    nparr = np.frombuffer(image_bytes, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    
    if image is None:
        raise HTTPException(status_code=400, detail="Invalid image format")
    
    # Assess image quality
    quality = await loop.run_in_executor(
        executor, ImageQualityAssessor.assess_quality, image
    )
    
    # If quality is too poor, return early
    if quality.overall_score < 30:
        return quality, InsuranceCardData()
    
    # Preprocess image
    processed_image = await loop.run_in_executor(
        executor, InsuranceCardProcessor.preprocess_image, image
    )
    
    # Convert processed image back to bytes for Vision API
    _, buffer = cv2.imencode('.jpg', processed_image)
    processed_bytes = buffer.tobytes()
    
    # Extract text using Vision API
    text, annotations = InsuranceCardProcessor.extract_text_with_vision_api(processed_bytes)
    
    # Parse insurance data
    insurance_data = await loop.run_in_executor(
        executor, InsuranceCardProcessor.parse_insurance_data, text, annotations
    )
    
    return quality, insurance_data

@app.post("/upload-insurance-card")
async def upload_insurance_card(file: UploadFile = File(...)):
    """Upload and process insurance card image"""
    
    # Validate file type
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    try:
        # Read image bytes
        image_bytes = await file.read()
        
        if len(image_bytes) == 0:
            raise HTTPException(status_code=400, detail="Empty file")
        
        # Process image
        quality, insurance_data = await process_image_async(image_bytes)
        
        # Prepare response with explicit type conversion
        response = {
            "timestamp": datetime.now().isoformat(),
            "filename": file.filename,
            "image_quality": {
                "is_good_quality": bool(quality.is_good_quality),
                "overall_score": float(round(quality.overall_score, 2)),
                "scores": {
                    "blur": float(round(quality.blur_score, 2)),
                    "brightness": float(round(quality.brightness_score, 2)),
                    "contrast": float(round(quality.contrast_score, 2)),
                    "resolution": float(round(quality.resolution_score, 2))
                },
                "issues": quality.issues
            },
            "extracted_data": {
                # Core identification (1-3)
                "member_id": insurance_data.member_id,                    # 1. Member ID Number
                "member_name": insurance_data.member_name,                # 2. Member Name  
                "group_number": insurance_data.group_number,              # 3. Group Number
                
                # Provider and plan info (4-5)
                "pcp_name": insurance_data.pcp_name,                      # 4. Primary Care Provider Name
                "pcp_phone": insurance_data.pcp_phone,                    # 4. Primary Care Provider Phone
                "plan_type": insurance_data.plan_type,                    # 5. Plan Type
                
                # Copay information (6-8)
                "copay_pcp": insurance_data.copay_pcp,                    # 6. Co-Pay for PCP visits
                "copay_specialist": insurance_data.copay_specialist,      # 7. Co-Pay for Specialty Care
                "copay_emergency": insurance_data.copay_emergency,        # 8. Emergency Care Co-Pay  
                "copay_urgent_care": insurance_data.copay_urgent_care,    # 8. Urgent Care Co-Pay
                
                # Prescription and additional info (9-10)
                "rx_info": insurance_data.rx_info,                       # 9. Prescription Drug Plan Info
                "plan_website": insurance_data.plan_website,             # 10. Health Plan Website
                
                # Deductibles and coinsurance (11-12)
                "in_network_deductible": insurance_data.in_network_deductible,      # 11. In-Network Deductible
                "in_network_coinsurance": insurance_data.in_network_coinsurance,    # 11. In-Network Coinsurance
                "oon_deductible": insurance_data.oon_deductible,                    # 12. Out-of-Network Deductible
                "oon_coinsurance": insurance_data.oon_coinsurance,                  # 12. Out-of-Network Coinsurance
                
                # Contact information (13)
                "plan_phone": insurance_data.plan_phone,                 # 13. Plan Contact Phone
                "customer_service_phone": insurance_data.customer_service_phone,    # 13. Customer Service Phone
                
                # Additional fields
                "insurance_company": insurance_data.insurance_company,
                "effective_date": insurance_data.effective_date
            },
            "confidence_scores": insurance_data.confidence_scores or {},
            "processing_successful": bool(quality.overall_score >= 30)
        }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.post("/assess-image-quality")
async def assess_image_quality(file: UploadFile = File(...)):
    """Quick image quality assessment endpoint"""
    
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    try:
        image_bytes = await file.read()
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if image is None:
            raise HTTPException(status_code=400, detail="Invalid image format")
        
        loop = asyncio.get_event_loop()
        quality = await loop.run_in_executor(
            executor, ImageQualityAssessor.assess_quality, image
        )
        
        return {
            "is_good_quality": quality.is_good_quality,
            "overall_score": round(quality.overall_score, 2),
            "detailed_scores": {
                "blur": round(quality.blur_score, 2),
                "brightness": round(quality.brightness_score, 2),
                "contrast": round(quality.contrast_score, 2),
                "resolution": round(quality.resolution_score, 2)
            },
            "issues": quality.issues,
            "recommendations": [
                "Ensure good lighting" if quality.brightness_score < 50 else None,
                "Hold camera steady" if quality.blur_score < 40 else None,
                "Move closer to the card" if quality.resolution_score < 60 else None,
                "Ensure card is flat and visible" if quality.contrast_score < 40 else None
            ]
        }
        
    except Exception as e:
        logger.error(f"Quality assessment error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Quality assessment failed: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Insurance Card OCR API",
        "version": "1.0.0",
        "endpoints": {
            "upload": "/upload-insurance-card",
            "quality": "/assess-image-quality",
            "health": "/health",
            "docs": "/docs"
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "gcp_insurance_ocr_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

