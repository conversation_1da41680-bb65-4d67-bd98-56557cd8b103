google/cloud/vision/__init__.py,sha256=J2Bi6uVQC55lJUDMd9M6sKUN3HB1AE-un_3kRgSTXZ8,5849
google/cloud/vision/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision/__pycache__/gapic_version.cpython-311.pyc,,
google/cloud/vision/gapic_version.py,sha256=xVXaiLR2CqjJ9g9If4DorgRxDSYPvrvnMD1ZolzG6Us,653
google/cloud/vision/py.typed,sha256=vwONhTeA1Ue-UwsTUNsSN9DoowRxEUoAtFRBvpB4NHw,80
google/cloud/vision_helpers/__init__.py,sha256=Ch0ZDMM0T3RgaNQTFOZtsCiT2_9A0cS_UKxK2v8xk_w,3638
google/cloud/vision_helpers/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_helpers/__pycache__/decorators.cpython-311.pyc,,
google/cloud/vision_helpers/decorators.py,sha256=1KaR2Ngq3SYq3hCltNlXS0ec9T5ZP0DNEZ6EhGNCUNQ,4181
google/cloud/vision_v1/__init__.py,sha256=pDP-We2DHqzbaSOKUUFS8urwYhi_2eMTl4tebYsgY0A,5834
google/cloud/vision_v1/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1/__pycache__/gapic_version.cpython-311.pyc,,
google/cloud/vision_v1/gapic_metadata.json,sha256=18v_N5bIueNuYe2-tVdZh3nnCr-eJ2uXx-MpEc0Srx0,10150
google/cloud/vision_v1/gapic_version.py,sha256=xVXaiLR2CqjJ9g9If4DorgRxDSYPvrvnMD1ZolzG6Us,653
google/cloud/vision_v1/py.typed,sha256=vwONhTeA1Ue-UwsTUNsSN9DoowRxEUoAtFRBvpB4NHw,80
google/cloud/vision_v1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/vision_v1/services/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/__init__.py,sha256=F77Nz3ytBuvpLva0IIVacR9wB3nf1JL49--yZmpTSnM,769
google/cloud/vision_v1/services/image_annotator/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/__pycache__/async_client.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/__pycache__/client.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/async_client.py,sha256=InUULrytAHN7y4ZKOhfmfoun5hdkUUhB4rHT0_Xx0Ag,36869
google/cloud/vision_v1/services/image_annotator/client.py,sha256=VjRJOoNWwK0u3t-sKhFKEl1eL8VmkNwfgWsfFFHdv1o,53917
google/cloud/vision_v1/services/image_annotator/transports/__init__.py,sha256=SVomYdRK90ystZjkjBI-BEsFWJIvj523bwCHt9aA-q0,1400
google/cloud/vision_v1/services/image_annotator/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/transports/__pycache__/rest_base.cpython-311.pyc,,
google/cloud/vision_v1/services/image_annotator/transports/base.py,sha256=9Rvwqz6L8o3NQ2hXFsSsao8LrPAyHsmcNRe5KGY374o,10361
google/cloud/vision_v1/services/image_annotator/transports/grpc.py,sha256=ijA0LdUHMxXW0oP--gCQRGsVVztaM9mBL4BklbWcmAM,22763
google/cloud/vision_v1/services/image_annotator/transports/grpc_asyncio.py,sha256=kTNtad4TGPno4wdMMMHxKiWa0G7R_WdQEZOy1XrOAfg,26392
google/cloud/vision_v1/services/image_annotator/transports/rest.py,sha256=XLmBE0dwI32Hv0UNNA6ri6GLii-TUyIhFYsz-r3hWV4,53869
google/cloud/vision_v1/services/image_annotator/transports/rest_base.py,sha256=5DgwMZgCo1qR_ntBln-paAahduMHDc2cKG8tYu93P7o,14267
google/cloud/vision_v1/services/product_search/__init__.py,sha256=7hBOliEgZhPjvs933eP-M2BYZ8fYqQNW1W8vY7hJAL4,765
google/cloud/vision_v1/services/product_search/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/__pycache__/async_client.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/__pycache__/client.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/__pycache__/pagers.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/async_client.py,sha256=iUdH4PMstxFXK9vmAFWoadiNY__5um5pwf4kFHT3ujM,121949
google/cloud/vision_v1/services/product_search/client.py,sha256=ur_lsPcdOSa-U0NWduoR8L-DlWfTjkAa_rW3cFJz45U,138241
google/cloud/vision_v1/services/product_search/pagers.py,sha256=Cy18oBR7nbMNuTzSUcQq-yMu6i4Gcyaoy1S9M_qUiGs,27940
google/cloud/vision_v1/services/product_search/transports/__init__.py,sha256=s1KeXtUiJAbFb-GLAyfaMq9RzHYWtBQa49Vx-955LUE,1386
google/cloud/vision_v1/services/product_search/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/transports/__pycache__/rest_base.cpython-311.pyc,,
google/cloud/vision_v1/services/product_search/transports/base.py,sha256=sPI1hnmlvPL0Wbm6wIRQu_XTQVfNTCcwgEILP3po_GU,23069
google/cloud/vision_v1/services/product_search/transports/grpc.py,sha256=dUTVzvthjf6Y3JwRQu76RUuqE4jfKX3cLJU1XRvCkx8,46050
google/cloud/vision_v1/services/product_search/transports/grpc_asyncio.py,sha256=99tlf3Ls4ytsNH0tbh0ToUtgROKJKEh7YEum82a3m_Y,58345
google/cloud/vision_v1/services/product_search/transports/rest.py,sha256=8KuljMBlLkfBMlYcehZ1h8v0zX__2mBqovO9fhYkaUM,173619
google/cloud/vision_v1/services/product_search/transports/rest_base.py,sha256=jlNTQws6qrCZcNlAjR10eE1ogGV2KNY1r9d3PMDo2O0,39430
google/cloud/vision_v1/types/__init__.py,sha256=U7k0lJp-NWfGO46hfxiod17C1xVip0472vqWMShpEow,5032
google/cloud/vision_v1/types/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1/types/__pycache__/geometry.cpython-311.pyc,,
google/cloud/vision_v1/types/__pycache__/image_annotator.cpython-311.pyc,,
google/cloud/vision_v1/types/__pycache__/product_search.cpython-311.pyc,,
google/cloud/vision_v1/types/__pycache__/product_search_service.cpython-311.pyc,,
google/cloud/vision_v1/types/__pycache__/text_annotation.cpython-311.pyc,,
google/cloud/vision_v1/types/__pycache__/web_detection.cpython-311.pyc,,
google/cloud/vision_v1/types/geometry.py,sha256=JsZ7ThiY6KKdn7Bx49vJ4HQ4O9JSqBXvfHfIsvYw8xc,3162
google/cloud/vision_v1/types/image_annotator.py,sha256=mRqar1Wf4XWNH2C-4mJTaV7sOFirHOZPRThe-xSnGeo,60056
google/cloud/vision_v1/types/product_search.py,sha256=oP--TgSnBqHqDUbhmSpk6i-4lpq2pTJDNxwL-p0Ld0E,8055
google/cloud/vision_v1/types/product_search_service.py,sha256=hgo5tgeYYdU7usq0U2ZRoUUbukh1rmaY_FG_dWwMmVE,34297
google/cloud/vision_v1/types/text_annotation.py,sha256=mimh_Q0TQAxVzbjaOxbO3wFNZ8IzS4ofJnBsjXkVU4U,14107
google/cloud/vision_v1/types/web_detection.py,sha256=n6uTqb7KsgwBzpTGlOsZVJTQWFYggehTcD8mKu1JAv0,6797
google/cloud/vision_v1p1beta1/__init__.py,sha256=E3_D-owJs35lihSZO796QNCT2bfsGftZfXO7MPYgxR8,2654
google/cloud/vision_v1p1beta1/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/__pycache__/gapic_version.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/gapic_metadata.json,sha256=n4Cbi0eUrjr8-grXTnQ3dXst-O-SF1H4-r_Q0KnM3ss,1073
google/cloud/vision_v1p1beta1/gapic_version.py,sha256=xVXaiLR2CqjJ9g9If4DorgRxDSYPvrvnMD1ZolzG6Us,653
google/cloud/vision_v1p1beta1/py.typed,sha256=vwONhTeA1Ue-UwsTUNsSN9DoowRxEUoAtFRBvpB4NHw,80
google/cloud/vision_v1p1beta1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/vision_v1p1beta1/services/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/__init__.py,sha256=F77Nz3ytBuvpLva0IIVacR9wB3nf1JL49--yZmpTSnM,769
google/cloud/vision_v1p1beta1/services/image_annotator/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/__pycache__/async_client.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/__pycache__/client.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/async_client.py,sha256=UVuIkTVerpwhW8a4kt3OMkqcGmfLyQxoF-nW3qsbFLs,17455
google/cloud/vision_v1p1beta1/services/image_annotator/client.py,sha256=K2z5Y9L4s-BgBmrrAJjdmL0FW75CLlPUUp7_H3hcL08,33492
google/cloud/vision_v1p1beta1/services/image_annotator/transports/__init__.py,sha256=SVomYdRK90ystZjkjBI-BEsFWJIvj523bwCHt9aA-q0,1400
google/cloud/vision_v1p1beta1/services/image_annotator/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/transports/__pycache__/rest_base.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/services/image_annotator/transports/base.py,sha256=LjvVWe37u1eStt9CBCXk10FjQ8IV20gheU-TSIm5RhE,7010
google/cloud/vision_v1p1beta1/services/image_annotator/transports/grpc.py,sha256=IRAR4EfnowPi596HladTFviEDMQQj6G92rwkPMWTpF0,16192
google/cloud/vision_v1p1beta1/services/image_annotator/transports/grpc_asyncio.py,sha256=X7sp1wX-tSPyvWcjqYBqeHhX7Jp212sQ3dptJgGwUC0,17709
google/cloud/vision_v1p1beta1/services/image_annotator/transports/rest.py,sha256=PiBkWb512fdYISZcB_coCIkoubna-HRiCGtuoxSZJB8,16804
google/cloud/vision_v1p1beta1/services/image_annotator/transports/rest_base.py,sha256=ml_WuRYOdCIo-KBsHVmgcfcRS15XNvziqAkoeV0uHa8,5541
google/cloud/vision_v1p1beta1/types/__init__.py,sha256=GRb9Dh2AwZlbaU2CWcekEYnHqV1F7nysZGoSP3xa6kk,1999
google/cloud/vision_v1p1beta1/types/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/types/__pycache__/geometry.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/types/__pycache__/image_annotator.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/types/__pycache__/text_annotation.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/types/__pycache__/web_detection.cpython-311.pyc,,
google/cloud/vision_v1p1beta1/types/geometry.py,sha256=fXfp9w6TFDwmQn0cx09Ae59E0VfHnNqRME3xsp7DQ6g,2371
google/cloud/vision_v1p1beta1/types/image_annotator.py,sha256=949KuySUL-Yk3OKVXgpB-6m801gkVnQuzh3n0sTnQYw,36802
google/cloud/vision_v1p1beta1/types/text_annotation.py,sha256=0aBzieTDzfkiLApnV6-U5WLpuKPSTAyF74bO8ZP8rCg,13945
google/cloud/vision_v1p1beta1/types/web_detection.py,sha256=QXsHaKmpASgDuKZexZkFKjVMRwfyvnaZV5nRnVmpUuM,6773
google/cloud/vision_v1p2beta1/__init__.py,sha256=sCYaUBz1KbYIbtDPMbOaxAtwQHLHoPIMeBc7vwc5L08,3280
google/cloud/vision_v1p2beta1/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/__pycache__/gapic_version.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/gapic_metadata.json,sha256=BH3safiyn45I8Q0ST5vIyD84ghAJkGsg94ZEhNDHCoA,1505
google/cloud/vision_v1p2beta1/gapic_version.py,sha256=xVXaiLR2CqjJ9g9If4DorgRxDSYPvrvnMD1ZolzG6Us,653
google/cloud/vision_v1p2beta1/py.typed,sha256=vwONhTeA1Ue-UwsTUNsSN9DoowRxEUoAtFRBvpB4NHw,80
google/cloud/vision_v1p2beta1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/vision_v1p2beta1/services/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/__init__.py,sha256=F77Nz3ytBuvpLva0IIVacR9wB3nf1JL49--yZmpTSnM,769
google/cloud/vision_v1p2beta1/services/image_annotator/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/__pycache__/async_client.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/__pycache__/client.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/async_client.py,sha256=zxSW4nFpmozeTmrQTRP8jB--tSccsWuXeXk1GHI72Y8,23211
google/cloud/vision_v1p2beta1/services/image_annotator/client.py,sha256=o2BAEiWhXgyW7x9YPh3Cenw20thfsB3gZAgVFNKTjYM,39148
google/cloud/vision_v1p2beta1/services/image_annotator/transports/__init__.py,sha256=SVomYdRK90ystZjkjBI-BEsFWJIvj523bwCHt9aA-q0,1400
google/cloud/vision_v1p2beta1/services/image_annotator/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/transports/__pycache__/rest_base.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/services/image_annotator/transports/base.py,sha256=LJRZA9gHNQChDafW8AMeHRY2vRJ_27zP9tHbDMbSegs,8130
google/cloud/vision_v1p2beta1/services/image_annotator/transports/grpc.py,sha256=Y6DaCwytRvPh9dS3etEmSdQPPyfmUNoGIHG-8yeRWnM,18583
google/cloud/vision_v1p2beta1/services/image_annotator/transports/grpc_asyncio.py,sha256=qCOuKdRa4cNtbAUau5QwsxumJu4l2yPdCmks-NHscBM,20760
google/cloud/vision_v1p2beta1/services/image_annotator/transports/rest.py,sha256=S6i3v3t5jhrVaNlLIM58X7M9S8zb6RMpQboIG5IzVsY,27603
google/cloud/vision_v1p2beta1/services/image_annotator/transports/rest_base.py,sha256=JvKDJXb9LDWOjIDbS4eIUtCv7Ix-r41avn4p3OVr73U,7563
google/cloud/vision_v1p2beta1/types/__init__.py,sha256=qpvy5wj8oqVBiucj7BpVQOr5zrHbaaJDZvaniUUYSw0,2625
google/cloud/vision_v1p2beta1/types/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/types/__pycache__/geometry.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/types/__pycache__/image_annotator.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/types/__pycache__/text_annotation.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/types/__pycache__/web_detection.cpython-311.pyc,,
google/cloud/vision_v1p2beta1/types/geometry.py,sha256=LnIGpialCCpbuxlZ4LYsoeoaxPgRZfh7ysH12If8ve0,3183
google/cloud/vision_v1p2beta1/types/image_annotator.py,sha256=Bv7KsGtKGwlBuk_jESX8EIrUxvhkka3pw9F2-QPzXl0,47008
google/cloud/vision_v1p2beta1/types/text_annotation.py,sha256=upRdiAWj3VBoYslaVI8-9cALjCjWykkKzj1oVZN8Xpo,14254
google/cloud/vision_v1p2beta1/types/web_detection.py,sha256=PV-NLimE_uV7DHtDu75yHfEB08nBaOfVlQmUgJLy_f4,6773
google/cloud/vision_v1p3beta1/__init__.py,sha256=gnTYp_Oxmv9OCkwLGk1w60g0tzb5Ccp94k_3td0TkdM,5395
google/cloud/vision_v1p3beta1/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/__pycache__/gapic_version.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/gapic_metadata.json,sha256=_TeFP2FEgxYicLy9ylLCDg_p-i8i_XwCyfyx_tL0-z0,8961
google/cloud/vision_v1p3beta1/gapic_version.py,sha256=xVXaiLR2CqjJ9g9If4DorgRxDSYPvrvnMD1ZolzG6Us,653
google/cloud/vision_v1p3beta1/py.typed,sha256=vwONhTeA1Ue-UwsTUNsSN9DoowRxEUoAtFRBvpB4NHw,80
google/cloud/vision_v1p3beta1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/vision_v1p3beta1/services/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/__init__.py,sha256=F77Nz3ytBuvpLva0IIVacR9wB3nf1JL49--yZmpTSnM,769
google/cloud/vision_v1p3beta1/services/image_annotator/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/__pycache__/async_client.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/__pycache__/client.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/async_client.py,sha256=JXfQsXBp_3UkZyR5beUPi4EANpXT-vzInfufh99LO2Q,23525
google/cloud/vision_v1p3beta1/services/image_annotator/client.py,sha256=vJP380NZYrxZM2cdkQ_6JjaXc9gxf6AjGlGPUZmz0n8,40620
google/cloud/vision_v1p3beta1/services/image_annotator/transports/__init__.py,sha256=SVomYdRK90ystZjkjBI-BEsFWJIvj523bwCHt9aA-q0,1400
google/cloud/vision_v1p3beta1/services/image_annotator/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/transports/__pycache__/rest_base.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/image_annotator/transports/base.py,sha256=S9Jy0BSqHMs-K_x-oahAl6O51foOs8OFVl676zmEtcE,7991
google/cloud/vision_v1p3beta1/services/image_annotator/transports/grpc.py,sha256=8lqTi0miqo5p0LgrE8LmA_Nxh_vpf-5pGg5CuNDkeDg,18599
google/cloud/vision_v1p3beta1/services/image_annotator/transports/grpc_asyncio.py,sha256=5tKr5K6U6deeR_eIckmQseg1vwJsx4axqC3ZqOVvEss,20637
google/cloud/vision_v1p3beta1/services/image_annotator/transports/rest.py,sha256=tlZGyuqCG50a0kcXeET-Wb-Wllhu7AILZ2DRhVl5qT4,27603
google/cloud/vision_v1p3beta1/services/image_annotator/transports/rest_base.py,sha256=ESsS9RqZzfuwOIo3ckOGvT3HVCuTe4ea1c3wZoCkHls,7563
google/cloud/vision_v1p3beta1/services/product_search/__init__.py,sha256=7hBOliEgZhPjvs933eP-M2BYZ8fYqQNW1W8vY7hJAL4,765
google/cloud/vision_v1p3beta1/services/product_search/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/__pycache__/async_client.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/__pycache__/client.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/__pycache__/pagers.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/async_client.py,sha256=nqSMjTV1ba9AZWNAs80YNEGAm5EZfghsOvcsnBnPAMo,113749
google/cloud/vision_v1p3beta1/services/product_search/client.py,sha256=Yf2n9V3yrY3jfcPhmvGlYFjsfP871XI4ZaG_rom8fjI,130031
google/cloud/vision_v1p3beta1/services/product_search/pagers.py,sha256=Go2qyR5AYgWTzuJnzL8c_2KSqPmL-fpScYjbkIak-EQ,28171
google/cloud/vision_v1p3beta1/services/product_search/transports/__init__.py,sha256=s1KeXtUiJAbFb-GLAyfaMq9RzHYWtBQa49Vx-955LUE,1386
google/cloud/vision_v1p3beta1/services/product_search/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/transports/__pycache__/rest_base.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/services/product_search/transports/base.py,sha256=ZpoRQQmAeTzTSv6Yk5vesevrsrJErPp51GTc0SE4bbA,21394
google/cloud/vision_v1p3beta1/services/product_search/transports/grpc.py,sha256=4WNqczlxwpmFjhZNVzLCbOeSwLLYDTuDIVDvPWkOrZg,43282
google/cloud/vision_v1p3beta1/services/product_search/transports/grpc_asyncio.py,sha256=CQmz0FC9kY8fy2nvjkmJbJTgEJgC_Gc9dpuT9zeMUaQ,54358
google/cloud/vision_v1p3beta1/services/product_search/transports/rest.py,sha256=-Vtf3UXjsBXG1XvLCM4MS9fFnT_tOsyvP73MVgzNc1A,157902
google/cloud/vision_v1p3beta1/services/product_search/transports/rest_base.py,sha256=dM0A5yuNMp83Ivh4ly-yMGgxOy9R31SZH0667e2Qooo,36280
google/cloud/vision_v1p3beta1/types/__init__.py,sha256=bVfQEjZF4POFx6_xjVyHsOUsWbt_Bthfw4WxzqpJ5Wg,4586
google/cloud/vision_v1p3beta1/types/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/types/__pycache__/geometry.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/types/__pycache__/image_annotator.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/types/__pycache__/product_search.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/types/__pycache__/product_search_service.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/types/__pycache__/text_annotation.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/types/__pycache__/web_detection.cpython-311.pyc,,
google/cloud/vision_v1p3beta1/types/geometry.py,sha256=lCCljnv5N2mWkFaB60efkDH5su-KXMBRzdpQlZMKlNM,3183
google/cloud/vision_v1p3beta1/types/image_annotator.py,sha256=ha8sz_Wsq4CQXwzCCz0teOLt-vGePc-__x4Ogn8WIJI,49678
google/cloud/vision_v1p3beta1/types/product_search.py,sha256=5Zdnf10IavDJWqCXexo-2OCcnrBOtsU5x-Dd2XupCaI,8125
google/cloud/vision_v1p3beta1/types/product_search_service.py,sha256=rLo4McD_sCiC-7cohlBDMsexglcKPYUnXE1-GmK6jPc,31432
google/cloud/vision_v1p3beta1/types/text_annotation.py,sha256=Y-iPUwelzUWiGbUOAaFyhZ-_xeyj73LYyNNApDL61GI,14254
google/cloud/vision_v1p3beta1/types/web_detection.py,sha256=6sYQbW-GLvE7FTcgzk6D61FXBf36r39fZ4-8aRN8Yas,6773
google/cloud/vision_v1p4beta1/__init__.py,sha256=zY7hZKC4Ikcikj-uEsX86nb04DCB2lH9JVyRuydgG10,5996
google/cloud/vision_v1p4beta1/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/__pycache__/gapic_version.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/gapic_metadata.json,sha256=7qTGN2QTjqdtnYcsDMGPrHoDjrKNuxzgxgkDELWz820,10164
google/cloud/vision_v1p4beta1/gapic_version.py,sha256=xVXaiLR2CqjJ9g9If4DorgRxDSYPvrvnMD1ZolzG6Us,653
google/cloud/vision_v1p4beta1/py.typed,sha256=vwONhTeA1Ue-UwsTUNsSN9DoowRxEUoAtFRBvpB4NHw,80
google/cloud/vision_v1p4beta1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/vision_v1p4beta1/services/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/__init__.py,sha256=F77Nz3ytBuvpLva0IIVacR9wB3nf1JL49--yZmpTSnM,769
google/cloud/vision_v1p4beta1/services/image_annotator/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/__pycache__/async_client.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/__pycache__/client.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/async_client.py,sha256=jH2qWpjD47Xcxo34W1-EtRIpA5YgLgFiG5BwYQhNI9U,34767
google/cloud/vision_v1p4beta1/services/image_annotator/client.py,sha256=daRPuN0Pe_4OecebNLg8Mb3ZVrTfjnkavGJvwsiK1Dk,51677
google/cloud/vision_v1p4beta1/services/image_annotator/transports/__init__.py,sha256=SVomYdRK90ystZjkjBI-BEsFWJIvj523bwCHt9aA-q0,1400
google/cloud/vision_v1p4beta1/services/image_annotator/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/transports/__pycache__/rest_base.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/image_annotator/transports/base.py,sha256=QdtIIYF2MiGJWlReAANpDaHUJMEqD7O_8pcONaV36CY,9390
google/cloud/vision_v1p4beta1/services/image_annotator/transports/grpc.py,sha256=-cm5tDoVBf22t46RqxWyhvrk-33QzhM2ulFr6m-6r3I,21980
google/cloud/vision_v1p4beta1/services/image_annotator/transports/grpc_asyncio.py,sha256=yDyr9TCNHou-Gu0eEUki2URRL6klCjwG9Ji3ffZxy-k,24872
google/cloud/vision_v1p4beta1/services/image_annotator/transports/rest.py,sha256=oM4siTSiLNrDNU3SlFnd4_USPum5BZG4LUYYw1Rmy0c,46714
google/cloud/vision_v1p4beta1/services/image_annotator/transports/rest_base.py,sha256=PZi7s8ZoZe8I3up2IB6UEIcPk_0Qcd8sQIE6ByRDcbM,11462
google/cloud/vision_v1p4beta1/services/product_search/__init__.py,sha256=7hBOliEgZhPjvs933eP-M2BYZ8fYqQNW1W8vY7hJAL4,765
google/cloud/vision_v1p4beta1/services/product_search/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/__pycache__/async_client.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/__pycache__/client.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/__pycache__/pagers.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/async_client.py,sha256=CB6znqrPu2BhlCUBOhofMQEOgKBgjw1vUjqXC2GOc9U,120402
google/cloud/vision_v1p4beta1/services/product_search/client.py,sha256=h5h_zilV8611K6Z-cZo3q9o57K93XdckqPLO07Kj1AE,136556
google/cloud/vision_v1p4beta1/services/product_search/pagers.py,sha256=5cKc3nzUXQvkvCyDGVRXmmZWYTGpN6pFPi_Inb9P_yI,28171
google/cloud/vision_v1p4beta1/services/product_search/transports/__init__.py,sha256=s1KeXtUiJAbFb-GLAyfaMq9RzHYWtBQa49Vx-955LUE,1386
google/cloud/vision_v1p4beta1/services/product_search/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/transports/__pycache__/rest_base.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/services/product_search/transports/base.py,sha256=mNF7gnrpMiE6PKbiv1f4Z6Hpg10fmyNMDpDHeNreWks,21835
google/cloud/vision_v1p4beta1/services/product_search/transports/grpc.py,sha256=sX2acQEzTlbdPElZBYnwsS2cZ75IdlbEig-jFJWeNDc,45421
google/cloud/vision_v1p4beta1/services/product_search/transports/grpc_asyncio.py,sha256=qtADdyAXRtZo7g9-rykx6KeH8bv5ODCu83CIqaSk0zY,56711
google/cloud/vision_v1p4beta1/services/product_search/transports/rest.py,sha256=Fxy-So1sHym3yFJI0nsSKIBOkxvtX9oyEPAqC0vEEkg,166869
google/cloud/vision_v1p4beta1/services/product_search/transports/rest_base.py,sha256=U-b_Lub5J6HxQMZmSPDwkbgE39DmKlSgVJTC3IioL-Q,38238
google/cloud/vision_v1p4beta1/types/__init__.py,sha256=dLuuxBmlESw8tISMtlVu-F4Odmk1M6awSTDakYrn_bo,5181
google/cloud/vision_v1p4beta1/types/__pycache__/__init__.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/types/__pycache__/face.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/types/__pycache__/geometry.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/types/__pycache__/image_annotator.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/types/__pycache__/product_search.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/types/__pycache__/product_search_service.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/types/__pycache__/text_annotation.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/types/__pycache__/web_detection.cpython-311.pyc,,
google/cloud/vision_v1p4beta1/types/face.py,sha256=FCDsH67TGjzC608ezkkYV1kjrXx87TEdhkZDyxGfW7Y,2802
google/cloud/vision_v1p4beta1/types/geometry.py,sha256=rG2db826ou1vGtCwqPF7-FOlRNMV04xsYW3z4HIOTos,3183
google/cloud/vision_v1p4beta1/types/image_annotator.py,sha256=qH1u0Plcpb6W6RCrj-2B49IhzUzn5jLtO2xUnHBQpi4,56263
google/cloud/vision_v1p4beta1/types/product_search.py,sha256=BWcG4j2YtKjd9noGn9kvAb9EDxDumqVX-6c57kt3EKQ,8125
google/cloud/vision_v1p4beta1/types/product_search_service.py,sha256=ohYlmM1i6HtzZpbdXrmqBdc3poZnQOeT6jrzDVBHz3s,34409
google/cloud/vision_v1p4beta1/types/text_annotation.py,sha256=sIinJwlBuEcZgmyNyw8dTCALX6nQsUsGA0t-OeTvOAQ,14254
google/cloud/vision_v1p4beta1/types/web_detection.py,sha256=aIB4Etm7WlOlmwX38jmM3YyabYihnGp5Y3be3PLT-gU,6860
google_cloud_vision-3.10.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_vision-3.10.2.dist-info/METADATA,sha256=nDjApcsyccKBzNT6_r3ifx65P1KlKxmYl3ORZRFMWcc,9649
google_cloud_vision-3.10.2.dist-info/RECORD,,
google_cloud_vision-3.10.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
google_cloud_vision-3.10.2.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
google_cloud_vision-3.10.2.dist-info/licenses/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_vision-3.10.2.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
