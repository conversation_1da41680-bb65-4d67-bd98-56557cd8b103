{"comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "python", "libraryPackage": "google.cloud.vision_v1p2beta1", "protoPackage": "google.cloud.vision.v1p2beta1", "schema": "1.0", "services": {"ImageAnnotator": {"clients": {"grpc": {"libraryClient": "ImageAnnotatorClient", "rpcs": {"AsyncBatchAnnotateFiles": {"methods": ["async_batch_annotate_files"]}, "BatchAnnotateImages": {"methods": ["batch_annotate_images"]}}}, "grpc-async": {"libraryClient": "ImageAnnotatorAsyncClient", "rpcs": {"AsyncBatchAnnotateFiles": {"methods": ["async_batch_annotate_files"]}, "BatchAnnotateImages": {"methods": ["batch_annotate_images"]}}}, "rest": {"libraryClient": "ImageAnnotatorClient", "rpcs": {"AsyncBatchAnnotateFiles": {"methods": ["async_batch_annotate_files"]}, "BatchAnnotateImages": {"methods": ["batch_annotate_images"]}}}}}}}